<script>
  import ListGrid from "./ListGrid.svelte";
  import ListGridItem from "./ListGridItem.svelte";
</script>

<section id="features">
  <div class="container">
    <h1>Features</h1>
    <ListGrid>
      {#snippet children()}
        <ListGridItem
          title="No algorithms"
          description="Timelines show posts in chronological order. Only posts from feeds you've subscribed to show up. Once you've reached the end of the feed, no more content will be shown."
        />
        <ListGridItem
          title="Follow feeds, not people"
          description="Every user can have multiple feeds and are strongly encouraged to keep each feed to one theme."
        />
        <ListGridItem
          title="Control your timeline(s)"
          description="If you'd like, you can create multiple timelines. Then add a feed to a timeline of your choice!"
        />
        <ListGridItem
          title="Private feeds"
          description="Want to make a feed that only certain users can access? It's easy!"
        />
      {/snippet}
    </ListGrid>
  </div>
</section>

<style>
</style>
