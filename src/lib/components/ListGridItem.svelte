<script>
  let { title, description } = $props();
</script>

<li>
  <h2>{title}</h2>
  <p>{description}</p>
</li>

<style>
  li {
    padding: 1rem;
    transition: all 0.2s ease;
  }

  h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.75rem 0;
    line-height: 1.4;
  }

  p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #6b7280;
    margin: 0;
    transition: color 0.2s ease;
  }

  li:where(:hover, :focus-within) p {
    color: #374151;
  }
</style>
